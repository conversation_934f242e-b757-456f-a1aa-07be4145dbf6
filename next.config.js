/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { dev, isServer }) => {
    // Fix for ES module issues and wallet-related modules
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
      crypto: false,
      stream: false,
      url: false,
      zlib: false,
      http: false,
      https: false,
      assert: false,
      os: false,
      path: false,
    };

    // Handle pino-pretty optional dependency
    config.resolve.alias = {
      ...config.resolve.alias,
      'pino-pretty': false,
    };

    // Ignore specific modules that cause issues in server-side rendering
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push({
        'pino-pretty': 'pino-pretty',
        'lokijs': 'lokijs',
        'encoding': 'encoding',
      });


    }

    // Completely exclude problematic files from Terser minification
    if (!dev && config.optimization && config.optimization.minimizer) {
      config.optimization.minimizer = config.optimization.minimizer.map((minimizer) => {
        if (minimizer.constructor.name === 'TerserPlugin') {
          return {
            ...minimizer,
            options: {
              ...minimizer.options,
              exclude: [
                /HeartbeatWorker\.js$/,
                /\.worker\.js$/,
                /static\/media\/HeartbeatWorker\./,
              ],
              terserOptions: {
                ...minimizer.options.terserOptions,
                parse: {
                  ...minimizer.options.terserOptions?.parse,
                  ecma: 2020,
                },
                compress: {
                  ...minimizer.options.terserOptions?.compress,
                  module: true,
                },
                mangle: {
                  ...minimizer.options.terserOptions?.mangle,
                  module: true,
                },
                format: {
                  ...minimizer.options.terserOptions?.format,
                  comments: false,
                },
              },
            }
          };
        }
        return minimizer;
      });
    }

    return config;
  },
  transpilePackages: [
    '@coinbase/wallet-sdk',
    '@rainbow-me/rainbowkit',
    '@walletconnect/ethereum-provider',
    '@walletconnect/universal-provider',
    '@walletconnect/logger',
  ],
  eslint: {
    ignoreDuringBuilds: false,
  },
};

module.exports = nextConfig;