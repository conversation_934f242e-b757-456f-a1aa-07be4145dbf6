"use client"
import React, { createContext, useContext, useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { User, apiService, Application } from "./api"

interface AuthUser extends User {
  role: string
}

interface AuthContextType {
  user: AuthUser | null
  token: string | null
  selectedApp: Application | null
  setSelectedApp: (app: Application | null) => void
  login: (user: User, token: string, role?: string) => void
  logout: () => void
  isLoading: boolean
  refreshProfile: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [selectedApp, setSelectedApp] = useState<Application | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Check for existing session
    const storedToken = localStorage.getItem('authToken')
    const storedUser = localStorage.getItem('authUser')
    const storedRole = localStorage.getItem('userRole')
    const storedSelectedApp = localStorage.getItem('selectedApp')
    
    if (storedToken && storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser)
        setToken(storedToken)
        setUser({ ...parsedUser, role: storedRole || 'developer' })
        
        // Restore selected app if available
        if (storedSelectedApp) {
          try {
            const app = JSON.parse(storedSelectedApp)
            setSelectedApp(app)
          } catch {
            localStorage.removeItem('selectedApp')
          }
        }
      } catch {
        // Clear invalid stored data
        localStorage.removeItem('authToken')
        localStorage.removeItem('authUser')
        localStorage.removeItem('userRole')
        localStorage.removeItem('selectedApp')
      }
    }
    setIsLoading(false)
  }, [])

  const login = (userData: User, authToken: string, role: string = 'developer') => {
    console.log('AuthContext: Logging in user with token:', authToken?.substring(0, 20) + '...')
    console.log('AuthContext: User data:', userData)

    const userWithRole = { ...userData, role }
    setUser(userWithRole)
    setToken(authToken)
    localStorage.setItem('authToken', authToken)
    localStorage.setItem('authUser', JSON.stringify(userData))
    localStorage.setItem('userRole', role)
  }

  const logout = () => {
    setUser(null)
    setToken(null)
    setSelectedApp(null)
    localStorage.removeItem('authToken')
    localStorage.removeItem('authUser')
    localStorage.removeItem('userRole')
    localStorage.removeItem('selectedApp')
    router.push('/auth/signin')
  }

  // Store selected app in local storage when it changes
  const handleSetSelectedApp = (app: Application | null) => {
    setSelectedApp(app)
    if (app) {
      localStorage.setItem('selectedApp', JSON.stringify(app))
    } else {
      localStorage.removeItem('selectedApp')
    }
  }

  const refreshProfile = async () => {
    if (!user || !token) return

    try {
      // Use the updated getProfile method that doesn't require user ID
      const response = await apiService.getProfile(token)
      if (response.success && response.data) {
        // The profile data is returned directly, not wrapped in a data property
        const profileData = response.data
        const updatedUser = {
          ...profileData,
          role: user.role,
          isVerified: profileData.isActive // Map isActive to isVerified for compatibility
        }
        setUser(updatedUser)
        localStorage.setItem('authUser', JSON.stringify(profileData))
      }
    } catch (error) {
      console.error('Failed to refresh profile:', error)
    }
  }

  return (
    <AuthContext.Provider value={{ 
      user, 
      token, 
      selectedApp, 
      setSelectedApp: handleSetSelectedApp, 
      login, 
      logout, 
      isLoading, 
      refreshProfile 
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}