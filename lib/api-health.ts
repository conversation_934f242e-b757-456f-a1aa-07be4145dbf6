// API Health Check Utility
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api.crefy-connect-v2.crefy.xyz/api/v1';

export interface HealthCheckResult {
  isHealthy: boolean;
  status: number | null;
  message: string;
  responseTime: number;
  timestamp: string;
}

export async function checkApiHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();
  
  try {
    console.log(`Checking API health at: ${API_BASE_URL}`);
    
    const response = await fetch(`${API_BASE_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Add timeout to prevent hanging
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });
    
    const responseTime = Date.now() - startTime;
    
    if (response.ok) {
      return {
        isHealthy: true,
        status: response.status,
        message: 'API is healthy and responding',
        responseTime,
        timestamp,
      };
    } else {
      return {
        isHealthy: false,
        status: response.status,
        message: `API returned status ${response.status}: ${response.statusText}`,
        responseTime,
        timestamp,
      };
    }
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    if (error instanceof Error && error.name === 'AbortError') {
      return {
        isHealthy: false,
        status: null,
        message: 'API request timed out after 10 seconds',
        responseTime,
        timestamp,
      };
    }
    
    return {
      isHealthy: false,
      status: null,
      message: error instanceof Error ? error.message : 'Unknown network error',
      responseTime,
      timestamp,
    };
  }
}

export function logApiHealthStatus(result: HealthCheckResult) {
  const logLevel = result.isHealthy ? 'info' : 'error';
  const logMessage = `[API Health Check] ${result.message} (${result.responseTime}ms)`;
  
  if (logLevel === 'error') {
    console.error(logMessage, result);
  } else {
    console.log(logMessage, result);
  }
}

// Utility to check if we should use mock responses
export function shouldUseMockResponses(): boolean {
  return process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_MOCK_API === 'true';
}