import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import jwt from 'jsonwebtoken';

// Mock database - in a real app, this would be replaced with actual database operations
const applications: Application[] = [];

interface Application {
  appId: string;
  name: string;
  allowedDomains: string[];
  createdAt: string;
  apiKey: string;
  developerId: string;
}

// Validation schemas
const createAppSchema = z.object({
  name: z.string().min(1, 'Application name is required').max(100),
  allowedDomains: z.array(z.string().url('Invalid domain URL')).min(1, 'At least one allowed domain is required'),
});

// Helper function to generate app credentials
function generateAppCredentials() {
  const appId = `app_${Date.now().toString(16)}${Math.random().toString(36).substring(2, 15)}`;
  const apiKey = `ak_${Date.now().toString(16)}${Math.random().toString(36).substring(2, 15)}`;
  return { appId, apiKey };
}

// JWT secret for token verification
const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret-key';

// Helper function to get developer ID from JWT token
function getDeveloperIdFromRequest(request: NextRequest): string {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Unauthorized');
  }

  const token = authHeader.substring(7);

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    return decoded.id || decoded.email; // Use id if available, fallback to email
  } catch (jwtError) {
    throw new Error('Unauthorized');
  }
}

export async function POST(request: NextRequest) {
  try {
    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        error: 'Invalid JSON in request body',
      }, { status: 400 });
    }
    
    // Check if body exists and has required properties
    if (!body || typeof body !== 'object') {
      return NextResponse.json({
        error: 'Request body is required',
      }, { status: 400 });
    }
    
    // Validate request body
    const validatedData = createAppSchema.parse(body);
    
    // Get developer ID from authentication
    const developerId = getDeveloperIdFromRequest(request);
    
    // Generate app credentials
    const { appId, apiKey } = generateAppCredentials();
    
    // Create new application
    const newApp: Application = {
      appId,
      name: validatedData.name,
      allowedDomains: validatedData.allowedDomains,
      createdAt: new Date().toISOString(),
      apiKey,
      developerId,
    };
    
    // Save to mock database
    applications.push(newApp);
    
    // Return response matching the API specification
    return NextResponse.json({
      appId: newApp.appId,
      name: newApp.name,
      allowedDomains: newApp.allowedDomains,
      createdAt: newApp.createdAt,
      apiKey: newApp.apiKey,
    }, { status: 201 });
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation error',
        details: error.errors,
      }, { status: 422 });
    }
    
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json({
        error: 'Unauthorized - Missing or invalid token',
      }, { status: 401 });
    }
    
    console.error('Error creating application:', error);
    return NextResponse.json({
      error: 'Bad request - Cannot destructure property from undefined request body',
    }, { status: 400 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get developer ID from authentication
    const developerId = getDeveloperIdFromRequest(request);
    
    // Filter applications by developer ID
    const developerApps = applications.filter(app => app.developerId === developerId);
    
    // Return applications without sensitive data (excluding apiKey)
    const sanitizedApps = developerApps.map(app => ({
      appId: app.appId,
      name: app.name,
      allowedDomains: app.allowedDomains,
      createdAt: app.createdAt,
    }));
    
    return NextResponse.json(sanitizedApps, { status: 200 });
    
  } catch (error) {
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json({
        error: 'Unauthorized - Authentication required',
      }, { status: 401 });
    }
    
    console.error('Error fetching applications:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
    }, { status: 500 });
  }
}