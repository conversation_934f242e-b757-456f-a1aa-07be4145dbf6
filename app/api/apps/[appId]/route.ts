import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import jwt from 'jsonwebtoken';

// JWT secret for token verification
const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret-key';

// Mock database - in a real app, this would be replaced with actual database operations
const applications: Application[] = [];

interface Application {
  appId: string;
  name: string;
  allowedDomains: string[];
  createdAt: string;
  updatedAt?: string;
  apiKey: string;
  developerId: string;
}

// Validation schema for updating app
const updateAppSchema = z.object({
  name: z.string().min(1, 'Application name is required').max(100).optional(),
  allowedDomains: z.array(z.string().url('Invalid domain URL')).min(1, 'At least one allowed domain is required').optional(),
});

// Helper function to get developer ID from JWT token
function getDeveloperIdFromRequest(request: NextRequest): string {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Unauthorized');
  }

  const token = authHeader.substring(7);
  
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    return decoded.id || decoded.email; // Use id if available, fallback to email
  } catch (jwtError) {
    throw new Error('Unauthorized');
  }
}

// GET /apps/{appId} - Get specific app by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ appId: string }> }
) {
  try {
    const { appId } = await params;
    
    // Validate appId
    if (!appId || typeof appId !== 'string') {
      return NextResponse.json({
        error: 'Invalid app ID',
      }, { status: 400 });
    }

    // Get developer ID from authentication
    const developerId = getDeveloperIdFromRequest(request);
    
    // Find the application
    const app = applications.find(a => a.appId === appId && a.developerId === developerId);
    
    if (!app) {
      return NextResponse.json({
        error: 'Application not found',
      }, { status: 404 });
    }

    // Return application data (matching Swagger spec)
    return NextResponse.json({
      appId: app.appId,
      name: app.name,
      allowedDomains: app.allowedDomains,
      createdAt: app.createdAt,
      updatedAt: app.updatedAt,
      apiKey: app.apiKey,
    }, { status: 200 });

  } catch (error) {
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json({
        error: 'Unauthorized - Authentication required',
      }, { status: 401 });
    }
    
    console.error('Error fetching application:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
    }, { status: 500 });
  }
}

// PUT /apps/{appId} - Update specific app
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ appId: string }> }
) {
  try {
    const { appId } = await params;
    
    // Validate appId
    if (!appId || typeof appId !== 'string') {
      return NextResponse.json({
        error: 'Invalid app ID',
      }, { status: 400 });
    }

    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        error: 'Invalid JSON in request body',
      }, { status: 400 });
    }

    // Check if body exists
    if (!body || typeof body !== 'object') {
      return NextResponse.json({
        error: 'Request body is required',
      }, { status: 400 });
    }

    // Validate request body
    const validatedData = updateAppSchema.parse(body);
    
    // Get developer ID from authentication
    const developerId = getDeveloperIdFromRequest(request);
    
    // Find the application
    const appIndex = applications.findIndex(a => a.appId === appId && a.developerId === developerId);
    
    if (appIndex === -1) {
      return NextResponse.json({
        error: 'Application not found',
      }, { status: 404 });
    }

    // Update the application
    const updatedApp: Application = {
      ...applications[appIndex],
      ...validatedData,
      updatedAt: new Date().toISOString(),
    };
    
    applications[appIndex] = updatedApp;

    // Return updated application data
    return NextResponse.json({
      appId: updatedApp.appId,
      name: updatedApp.name,
      allowedDomains: updatedApp.allowedDomains,
      createdAt: updatedApp.createdAt,
      updatedAt: updatedApp.updatedAt,
      apiKey: updatedApp.apiKey,
    }, { status: 200 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation error',
        details: error.errors,
      }, { status: 422 });
    }
    
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json({
        error: 'Unauthorized - Authentication required',
      }, { status: 401 });
    }
    
    console.error('Error updating application:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
    }, { status: 500 });
  }
}

// DELETE /apps/{appId} - Delete specific app
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ appId: string }> }
) {
  try {
    const { appId } = await params;
    
    // Validate appId
    if (!appId || typeof appId !== 'string') {
      return NextResponse.json({
        error: 'Invalid app ID',
      }, { status: 400 });
    }

    // Get developer ID from authentication
    const developerId = getDeveloperIdFromRequest(request);
    
    // Find the application
    const appIndex = applications.findIndex(a => a.appId === appId && a.developerId === developerId);
    
    if (appIndex === -1) {
      return NextResponse.json({
        error: 'Application not found',
      }, { status: 404 });
    }

    // Remove the application
    applications.splice(appIndex, 1);

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Application deleted successfully',
    }, { status: 200 });

  } catch (error) {
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json({
        error: 'Unauthorized - Authentication required',
      }, { status: 401 });
    }
    
    console.error('Error deleting application:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
    }, { status: 500 });
  }
}
